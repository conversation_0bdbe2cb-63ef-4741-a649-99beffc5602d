/**
 * Question Prompts Component Styles
 *
 * 极简设计的问题提示组件
 * 专注于功能性和可读性，去除所有装饰性元素
 */

/* 主容器 - 完全匹配chat input的布局 */

.question-prompts-main-title {
    font-size: 1.5rem; /* Adjusted title size */
    font-weight: 600;
    color: var(--color-text-primary);
    line-height: 1.3;
    margin-bottom: 0.75rem; /* Space before subtitle */
    text-align: left; /* Match subtitle alignment */
}

.question-prompts-container {
    width: 100%;
    margin: 0 auto;
    max-height: 60vh;
    overflow-y: auto;
}



.question-prompts-subtitle {
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--color-text-secondary);
    line-height: 1.4;
    margin-bottom: 1rem;
    text-align: left; /* 左对齐 */
}

/* 问题列表区域 - 带渐变效果的布局 */
.question-prompts-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 31rem; /* Further increased height for desktop */
    overflow-y: auto; /* Ensure scrollability */
    position: relative;
}



/* 问题项 - 极简设计 */
.question-prompt-item {
    display: flex;
    align-items: flex-start; /* 改为顶部对齐，确保多行文本时padding一致 */
    padding: 0.75vh 1vw; /* 适中的padding */
    background: transparent; /* 透明背景 */
    border: 1px solid var(--color-border-primary);
    border-radius: 0.5rem; /* 简单圆角 */
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
    min-height: 2.5rem; /* 适中的高度 */
    opacity: 0;
    animation: questionItemFadeIn 0.4s ease forwards;
}

.question-prompt-item:hover {
    background: var(--color-bg-secondary);
    border-color: var(--color-border-secondary);
}

.question-prompt-item:active {
    background: var(--color-bg-tertiary);
}

/* 问题文本 - 简洁排版 */
.question-prompt-text {
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--color-text-primary);
    line-height: 1.4;
    word-break: break-word;
    flex: 1;
}

/* Vue过渡动画 - 问题列表的进入和退出，带延迟 */
.question-list-enter-active {
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    transition-delay: 0.4s; /* 等待标题动画完成 */
}

.question-list-leave-active {
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.question-list-enter-from {
    opacity: 0;
    transform: translateY(15px);
}

.question-list-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

/* 即时隐藏过渡动画 - 用于切换到对话历史时 */
.question-list-instant-enter-active {
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    transition-delay: 0.4s; /* 等待标题动画完成 */
}

.question-list-instant-leave-active {
    transition: none; /* 立即消失，无动画 */
}

.question-list-instant-enter-from {
    opacity: 0;
    transform: translateY(15px);
}

.question-list-instant-leave-to {
    opacity: 0;
    /* 无需transform，因为没有过渡动画 */
}

/* 暗色模式适配 - 简洁设计 */
[data-theme="dark"] .question-prompt-item {
    border-color: var(--color-border-primary);
}

[data-theme="dark"] .question-prompt-item:hover {
    background: var(--color-bg-secondary);
    border-color: var(--color-border-secondary);
}

/* 简洁动画系统 */
@keyframes questionItemFadeIn {
    0% {
        opacity: 0;
        transform: translateY(8px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 - 简洁适配 */
@media (max-width: 768px) {
    .question-prompts-main-title {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
    }
    .question-prompts-subtitle {
        font-size: 0.8rem;
    }

    .question-prompt-item {
        padding: 0.75vh 2vw; /* 适中的padding */
        min-height: 2.25rem;
    }

    .question-prompt-text {
        font-size: 0.8rem;
    }

    .question-prompts-list {
        max-height: 60vh; /* Further increased height for tablet */
        gap: 0.375rem;
    }
}

@media (max-width: 640px) {
    .question-prompts-main-title {
        font-size: 1.1rem;
    }
    .question-prompt-item {
        padding: 0.75vh 2vw; /* 适中的padding */
        min-height: auto; /* 移除固定高度，让内容决定高度 */
    }

    .question-prompt-text {
        font-size: 0.875rem;
    }

    .question-prompts-list {
        max-height: 60vh;
    }
}

/* 骨架屏样式 */
.question-prompt-skeleton {
    cursor: default;
    pointer-events: none;
    background: var(--color-bg-secondary);
    border-color: var(--color-border-secondary);
}

.question-prompt-skeleton-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.skeleton-line {
    height: 0.875rem;
    background: linear-gradient(90deg,
        var(--color-bg-tertiary) 25%,
        var(--color-bg-secondary) 50%,
        var(--color-bg-tertiary) 75%);
    background-size: 200% 100%;
    border-radius: 0.25rem;
    animation: skeletonShimmer 1.5s infinite;
}

.skeleton-line-1 {
    width: 85%;
}

.skeleton-line-2 {
    width: 60%;
}

/* 骨架屏闪烁动画 */
@keyframes skeletonShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* 错误状态样式 */
.question-prompt-error {
    border-color: var(--color-border-warning);
    background: var(--color-bg-warning);
}

.question-prompt-error:hover {
    background: var(--color-bg-warning-hover);
}

/* 重试按钮区域 */
.question-prompts-retry {
    margin-top: 1rem;
    text-align: center;
}

.question-prompts-retry-btn {
    padding: 0.5rem 1rem;
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-primary);
    border-radius: 0.375rem;
    color: var(--color-text-secondary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.question-prompts-retry-btn:hover {
    background: var(--color-bg-secondary);
    border-color: var(--color-border-secondary);
    color: var(--color-text-primary);
}

/* 加载状态的副标题动画 */
.question-prompts-subtitle:has-text("正在为您生成") {
    animation: loadingPulse 2s infinite;
}

@keyframes loadingPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* 暗色模式下的骨架屏适配 */
[data-theme="dark"] .question-prompt-skeleton {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .skeleton-line {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 75%);
    background-size: 200% 100%;
}

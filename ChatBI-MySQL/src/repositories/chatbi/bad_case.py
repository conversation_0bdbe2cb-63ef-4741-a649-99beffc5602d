"""
Chat bad case repository module.

This module provides data access functions for managing bad case conversations.
"""

from mysql.connector import <PERSON><PERSON><PERSON>

from src.utils.logger import logger
from src.db.connection import execute_db_query

def mark_conversation_as_bad_case(conversation_id: str, is_bad_case: bool = True, marked_by: str = None) -> bool:
    """
    Mark or unmark a conversation as a bad case using the new bad_case table.

    Args:
        conversation_id (str): The ID of the conversation to mark
        is_bad_case (bool, optional): Whether to mark as bad case (True) or unmark (False). Defaults to True.
        marked_by (str, optional): The username of the person marking the bad case. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    if not conversation_id:
         logger.warning("Conversation ID is required to mark a bad case")
         return False

    try:
        if is_bad_case:
            # Mark as bad case - insert into bad_case table
            # First, verify the conversation exists
            check_sql = "SELECT COUNT(*) as count FROM chat_history WHERE conversation_id = %s LIMIT 1"
            result = execute_db_query(check_sql, (conversation_id,), fetch='one')

            if not result or result['count'] == 0:
                logger.warning(f"No chat history found for conversation {conversation_id}")
                return False

            # Insert into bad_case table (or update if exists)
            insert_sql = """
                INSERT INTO bad_case (conversation_id, marked_by)
                VALUES (%s, %s)
                ON DUPLICATE KEY UPDATE
                    marked_by = VALUES(marked_by),
                    repair_status = 0,
                    updated_at = CURRENT_TIMESTAMP
            """
            values = (conversation_id, marked_by)
            execute_db_query(insert_sql, values, commit=True)
            logger.info(f"Successfully marked conversation {conversation_id} as bad case by {marked_by}")

        else:
            # Unmark as bad case - remove from bad_case table
            delete_sql = "DELETE FROM bad_case WHERE conversation_id = %s"
            affected_rows = execute_db_query(delete_sql, (conversation_id,), fetch='count', commit=True)
            logger.info(f"Successfully unmarked conversation {conversation_id} as bad case, {affected_rows} records removed")

        return True

    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error marking conversation as bad case: {e}", exc_info=True)
        return False

def get_conversation_bad_case_status(conversation_id: str) -> bool:
    """
    Check if a conversation is marked as a bad case using the new bad_case table.

    Args:
        conversation_id (str): The ID of the conversation to check

    Returns:
        bool: True if the conversation is marked as a bad case, False otherwise
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to check bad case status")
        return False

    # Query the bad_case table
    sql = "SELECT id FROM bad_case WHERE conversation_id = %s"
    params = [conversation_id]

    # Note: username and email filters are no longer needed since we removed those fields
    # The conversation_id is unique per bad case, and user info can be obtained from chat_history
    # if username or email filters are needed, we can join with chat_history table

    # Limit to one row since we just need to check if the record exists
    sql += " LIMIT 1"

    try:
        result = execute_db_query(sql, tuple(params), fetch='one')
        # If result exists, the conversation is marked as a bad case
        return bool(result)
    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error checking bad case status: {e}", exc_info=True)
        return False





def update_bad_case_repair_status(conversation_id: str, repair_status: int) -> bool:
    """
    Update the repair status of a bad case.

    Args:
        conversation_id (str): The ID of the conversation
        repair_status (int): The new repair status (0=未修复, 1=已修复, 2=暂不修复)

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to update repair status")
        return False

    if repair_status not in [0, 1, 2]:
        logger.warning(f"Invalid repair status: {repair_status}. Must be 0, 1, or 2")
        return False

    # Update the repair status
    sql = "UPDATE bad_case SET repair_status = %s, updated_at = CURRENT_TIMESTAMP WHERE conversation_id = %s"
    params = [repair_status, conversation_id]

    try:
        affected_rows = execute_db_query(sql, tuple(params), fetch='count', commit=True)
        if affected_rows > 0:
            logger.info(f"Successfully updated repair status for conversation {conversation_id} to {repair_status}")
            return True
        else:
            logger.warning(f"No bad case found for conversation {conversation_id} to update repair status")
            return False
    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error updating repair status: {e}", exc_info=True)
        return False

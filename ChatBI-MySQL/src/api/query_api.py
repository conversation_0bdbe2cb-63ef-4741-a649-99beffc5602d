"""
Main query API endpoint for handling user queries.
"""
import uuid
from datetime import datetime

from flask import request, jsonify, Response, stream_with_context, Blueprint, session

from src.services.agent.runner import run_agent_query  # Updated to use new bot-based architecture
from src.services.auth.user_login_with_feishu import login_required
from src.services.chatbot.history_service import save_user_message
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email

# Create a Blueprint for query endpoints
# Using url_prefix='' to maintain original URL paths
query_bp = Blueprint('query', __name__, url_prefix='')


@query_bp.route('/query', methods=['POST'])
@login_required
def query_endpoint():
    """
    POST /query endpoint that receives JSON requests with a query field
    and returns model output as a streaming response.
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    job_title = user_info.get("job_title")

    if not username or not email:
        return jsonify({"error": "User information not found in session"}), 401

    data = request.get_json()
    if not data or "query" not in data:
        return jsonify({"error": "参数 'query' 不能为空"}), 400

    user_query = data["query"]
    images = data.get("images", [])  # 获取图片URL列表
    # history_for_agent = data.get("history", []) # History is now fetched by the runner
    conversation_id = data.get("conversation_id")  # Get existing ID or None for new chatbot
    user_timestamp = data.get("timestamp")  # Get user message timestamp from client
    is_retry = data.get("is_retry", False)

    if not user_timestamp:
        # Fallback if timestamp not provided by client (should be fixed in JS)
        user_timestamp = int(datetime.now().timestamp() * 1000)
        logger.warning(
            f"Timestamp not provided by client for query: {user_query}. Using server time.")

    # Generate a new conversation ID if one isn't provided
    if not conversation_id:
        conversation_id = str(uuid.uuid4())
        logger.info(f"Starting new conversation with ID: {conversation_id}")

    # Log received parameters (history is no longer passed)
    logger.info(
        f"User: {username} ({job_title}) | Convo: {conversation_id} | Query: {user_query} | Images: {len(images)} | is_retry: {is_retry}"
    )

    # --- Save User Message ---
    # Save user message only if it's not a retry (retries reuse the original user message)
    if not is_retry:
        try:
            save_user_message(
                username=username,
                email=email,
                conversation_id=conversation_id,
                content=user_query,
                timestamp=user_timestamp,
                images=images
            )
            logger.debug(f"User message saved for convo {conversation_id}")
        except Exception as e:
            logger.error(f"Error saving user message for convo {conversation_id}: {e}")
            # Decide if we should proceed or return an error
            # For now, let's proceed but log the error

    access_token = request.cookies.get("access_token")

    # Call the agent runner (without history argument)
    # The agent runner will fetch history and handle saving the assistant's response
    return Response(
        stream_with_context(
            run_agent_query(user_query, user_info, access_token, conversation_id, images) # Added images parameter
        ),
        content_type="text/event-stream",
        # Send conversation_id back in a header if it was newly generated
        headers={'X-Conversation-ID': conversation_id}
    )

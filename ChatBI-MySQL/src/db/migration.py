"""
Database migration module.

This module provides functions for migrating data from SQLite to MySQL.
"""

import os
import glob
import sqlite3
from pathlib import Path
from typing import Optional, Tuple
from src.utils.logger import logger
from mysql.connector import Error
from src.db.connection import get_db_connection

# --- Migration functionality ---

# Assume old history directory is specified via environment variable
OLD_HISTORY_DIR_PATH = os.getenv("OLD_HISTORY_DIR", str(Path(__file__).parent / "history"))
OLD_HISTORY_DIR = Path(OLD_HISTORY_DIR_PATH)


def _parse_username_email_from_filename(filename: str) -> Optional[Tuple[str, str]]:
    """
    Parse username and email from old SQLite filename.
    Filename format is assumed to be: history_{safe_username}_{safe_email}.db
    """
    try:
        # Extract the part between 'history_' and '.db'
        base_name = os.path.basename(filename)
        if not base_name.startswith('history_') or not base_name.endswith('.db'):
            return None
        
        middle_part = base_name[len('history_'):-len('.db')]
        
        # Find the last underscore to separate username and email
        last_underscore = middle_part.rfind('_')
        if last_underscore == -1:
            return None
        
        safe_username = middle_part[:last_underscore]
        safe_email = middle_part[last_underscore+1:]
        
        # Replace double underscores with @ for email
        email = safe_email.replace('__', '@')
        
        # Replace single underscores with spaces for username
        username = safe_username.replace('_', ' ')
        
        return username, email
    except Exception as e:
        logger.error(f"Error parsing filename {filename}: {e}")
        return None


def migrate_sqlite_to_mysql() -> Tuple[int, int, int]:
    """
    Migrate all SQLite history databases to MySQL.
    
    Returns:
        Tuple[int, int, int]: (total_files, success_count, error_count)
    """
    # Find all SQLite database files
    sqlite_files = glob.glob(str(OLD_HISTORY_DIR / "history_*.db"))
    
    total_files = len(sqlite_files)
    success_count = 0
    error_count = 0
    
    logger.info(f"Found {total_files} SQLite history files to migrate")
    
    for sqlite_file in sqlite_files:
        try:
            # Parse username and email from filename
            user_info = _parse_username_email_from_filename(sqlite_file)
            if not user_info:
                logger.warning(f"Could not parse username/email from {sqlite_file}, skipping")
                error_count += 1
                continue
                
            username, email = user_info
            
            # Migrate this file
            file_success = _migrate_single_file(sqlite_file, username, email)
            if file_success:
                success_count += 1
            else:
                error_count += 1
                
        except Exception as e:
            logger.error(f"Error migrating {sqlite_file}: {e}", exc_info=True)
            error_count += 1
    
    logger.info(f"Migration complete: {success_count} succeeded, {error_count} failed out of {total_files} files")
    return total_files, success_count, error_count


def _migrate_single_file(sqlite_file: str, username: str, email: str) -> bool:
    """
    Migrate a single SQLite database file to MySQL.
    
    Args:
        sqlite_file (str): Path to the SQLite database file
        username (str): Username parsed from the filename
        email (str): Email parsed from the filename
        
    Returns:
        bool: True if migration was successful, False otherwise
    """
    logger.info(f"Migrating {sqlite_file} for user {username} ({email})")
    
    # Connect to SQLite database
    try:
        sqlite_conn = sqlite3.connect(sqlite_file)
        sqlite_conn.row_factory = sqlite3.Row  # Use row factory to get dict-like rows
        sqlite_cursor = sqlite_conn.cursor()
        
        # Get all conversations
        sqlite_cursor.execute("SELECT DISTINCT conversation_id FROM history")
        conversation_ids = [row['conversation_id'] for row in sqlite_cursor.fetchall()]
        
        if not conversation_ids:
            logger.info(f"No conversations found in {sqlite_file}, skipping")
            sqlite_conn.close()
            return True  # Count as success since there's nothing to migrate
            
        logger.info(f"Found {len(conversation_ids)} conversations to migrate")
        
        # Connect to MySQL
        mysql_conn = get_db_connection('chatbi')
        
        # For each conversation, migrate all messages
        total_messages = 0
        for conversation_id in conversation_ids:
            # Get all messages for this conversation
            sqlite_cursor.execute(
                "SELECT * FROM history WHERE conversation_id = ? ORDER BY timestamp ASC",
                (conversation_id,)
            )
            messages = sqlite_cursor.fetchall()
            
            for message in messages:
                # Convert SQLite row to dict
                message_dict = dict(message)
                
                # Insert into MySQL
                sql = """
                    INSERT INTO chat_history 
                    (username, email, conversation_id, role, content, logs, timestamp, is_bad_case)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE id=id
                """
                
                # Check if is_bad_case exists in the message_dict
                is_bad_case = message_dict.get('is_bad_case', 0)
                if is_bad_case is None:
                    is_bad_case = 0
                
                values = (
                    username,
                    email,
                    message_dict['conversation_id'],
                    message_dict['role'],
                    message_dict['content'],
                    message_dict.get('logs'),  # May be None
                    message_dict['timestamp'],
                    is_bad_case
                )
                
                try:
                    from src.db.connection import execute_db_query
                    execute_db_query(sql, values, commit=True, database='chatbi')
                    total_messages += 1
                except Error as e:
                    logger.error(f"Error inserting message: {e}")
                    # Continue with next message
        
        logger.info(f"Successfully migrated {total_messages} messages for {len(conversation_ids)} conversations")
        
        # Close connections
        sqlite_conn.close()
        mysql_conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"Error migrating {sqlite_file}: {e}", exc_info=True)
        return False

"""
XianmuDB query execution service module.

This module provides business logic for executing queries against the XianmuDB business database.
"""
import asyncio
import concurrent.futures
from src.db.query import execute_sql_query
from src.utils.logger import logger
from src.db.database_enum import Database
from src.models.query_result import SQLQueryResult
from src.config.concurrency_config import ConcurrencyConfig

# 为数据库查询创建专用线程池，避免阻塞主事件循环
_DB_QUERY_EXECUTOR = concurrent.futures.ThreadPoolExecutor(
    max_workers=ConcurrencyConfig.DB_QUERY_THREAD_POOL_SIZE,
    thread_name_prefix="db_query_worker"
)

def execute_business_query(sql_query: str) -> SQLQueryResult:
    """
    Execute a SQL query against the XianmuDB business database.

    This function is specifically for querying the business database (xianmudb).
    It adds additional logging and error handling specific to business queries.

    Args:
        sql_query (str): The SQL query to execute

    Returns:
        SQLQueryResult: The result of the query
    """
    logger.debug(f"Executing business query against XianmuDB: {sql_query}")

    # Validate the query (basic check to prevent destructive operations)
    sql_lower = sql_query.lower().strip()
    if not sql_lower.startswith('select'):
        error_msg = "Only SELECT queries are allowed for business database"
        logger.warning(f"{error_msg}: {sql_query}")
        return SQLQueryResult(
            columns=None,
            data=None,
            error=error_msg,
            message=error_msg,
            success=False
        )

    try:
        # Execute the query against the business database
        result = execute_sql_query(sql_query, database=Database.BUSINESS)

        # Log the result
        if result.success:
            row_count = len(result.data) if result.data else 0
            logger.info(f"Business query executed successfully, returned {row_count} rows")
        else:
            logger.warning(f"Business query failed: {result.error}")

        return result

    except Exception as e:
        error_msg = f"Error executing business query: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return SQLQueryResult(
            columns=None,
            data=None,
            error=error_msg,
            message=error_msg,
            success=False
        )


async def execute_business_query_async(sql_query: str) -> SQLQueryResult:
    """
    异步执行业务数据库查询，避免阻塞事件循环。

    这个函数将同步的数据库查询操作放在专用线程池中执行，
    从而避免阻塞主事件循环，支持多用户并发查询。

    Args:
        sql_query (str): 要执行的SQL查询语句

    Returns:
        SQLQueryResult: 查询结果
    """
    logger.debug(f"异步执行业务查询: {sql_query}")

    try:
        # 在专用线程池中执行同步查询，避免阻塞事件循环
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            _DB_QUERY_EXECUTOR,
            execute_business_query,
            sql_query
        )

        if result.success:
            row_count = len(result.data) if result.data else 0
            logger.info(f"异步业务查询执行成功，返回 {row_count} 行")
        else:
            logger.warning(f"异步业务查询失败: {result.error}")

        return result

    except Exception as e:
        error_msg = f"异步执行业务查询时出错: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return SQLQueryResult(
            columns=None,
            data=None,
            error=error_msg,
            message=error_msg,
            success=False
        )

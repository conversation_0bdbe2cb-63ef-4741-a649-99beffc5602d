"""
Agent的模型提供者配置
"""
import os
from agents import set_tracing_disabled
from agents.extensions.models.litellm_model import LitellmModel
from .cache_enabled_litellm_model import CacheEnabledLitellmModel

# 基础模型配置
OPENAI_API_KEY = os.getenv("XM_OPENAI_API_KEY")
OPENAI_API_BASE = os.getenv("OPENAI_API_BASE")
OPENAI_MODEL = os.getenv("OPENAI_MODEL")
OPENAI_MODEL_SETTINGS = os.getenv("OPENAI_MODEL_SETTINGS")

# 快模型(LITE)配置
LITE_OPENAI_API_KEY = os.getenv("LITE_XM_OPENAI_API_KEY", OPENAI_API_KEY)
LITE_OPENAI_API_BASE = os.getenv("LITE_OPENAI_API_BASE", OPENAI_API_BASE)
LITE_OPENAI_MODEL = os.getenv("LITE_OPENAI_MODEL", OPENAI_MODEL)
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")

set_tracing_disabled(disabled=False)

# Litellm 模型配置 (模型名称需 "openai/" 前缀)
# LITE_LLM_FAST_MODEL = LitellmModel(model=f"openai/{LITE_OPENAI_MODEL}", api_key=LITE_OPENAI_API_KEY, base_url=LITE_OPENAI_API_BASE)
# LITE_LLM_MODEL = LitellmModel(model=f"openai/{OPENAI_MODEL}", api_key=OPENAI_API_KEY, base_url=OPENAI_API_BASE)

# 支持缓存的Litellm模型配置
CACHE_ENABLED_LITE_LLM_FAST_MODEL = CacheEnabledLitellmModel(model=f"openai/{LITE_OPENAI_MODEL}", api_key=LITE_OPENAI_API_KEY, base_url=LITE_OPENAI_API_BASE)
CACHE_ENABLED_LITE_LLM_MODEL = CacheEnabledLitellmModel(model=f"openai/{OPENAI_MODEL}", api_key=OPENAI_API_KEY, base_url=OPENAI_API_BASE)

# 启用缓存模型
# 请注意，在使用缓存模型时，需要将LITE_LLM_FAST_MODEL和LITE_LLM_MODEL替换为CACHE_ENABLED_LITE_LLM_FAST_MODEL和CACHE_ENABLED_LITE_LLM_MODEL
LITE_LLM_FAST_MODEL=CACHE_ENABLED_LITE_LLM_FAST_MODEL
LITE_LLM_MODEL=CACHE_ENABLED_LITE_LLM_MODEL

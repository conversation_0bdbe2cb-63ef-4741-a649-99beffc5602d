"""
Base bot class for all agent types.
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional

from agents import Agent,Model
from src.services.agent.utils.model_provider import LITE_LLM_MODEL


class BaseBot(ABC):
    """
    Base class for all bot implementations.

    This abstract class defines the interface that all bot types must implement.
    Each bot type should handle its own:
    - System instructions
    - Permission definitions
    - Tool selection
    - Model selection
    """

    def __init__(self, user_info: Dict[str, Any]):
        """
        Initialize the bot with user information.

        Args:
            user_info: Dictionary containing user information
        """
        self.user_info = user_info
        self.user_name = user_info.get("name", "未知用户")
        self.job_title = user_info.get("job_title", "未知职位")

    @abstractmethod
    def get_description(self) -> str:
        """
        Get a description of the bot's capabilities.

        Returns:
            str: Description of the bot's capabilities
        """
        pass

    @abstractmethod
    def create_agent(self, model: Optional[Model] = LITE_LLM_MODEL) -> Agent:
        """
        Create and return an agent instance.

        Args:
            model: Optional model instance to use (defaults to LITE_LLM_MODEL if not provided)

        Returns:
            Agent: The initialized agent
        """
        pass

"""
Data-related tools for agent bots.
"""

import uuid
from typing import Tuple, List, Dict, Any
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.services.agent.tools.feishu_tools import upload_sql_result_to_feishu_if_needed
from src.services.xianmudb.query_service import execute_business_query, execute_business_query_async
from src.services.xianmudb.metadata_service import (
    get_table_sample_data as get_table_sample_data_real,
)
from src.models.query_result import SQLQueryResult
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager
from src.services.concurrency.user_query_limiter import user_query_limiter

async def fetch_mysql_sql_result(
    wrapper: RunContextWrapper[UserInfo], sql: str, description: str, upload_to_feishu: bool = False
) -> Tuple[SQLQueryResult, str]:
    """使用SQL查询MySQL数据库，返回查询结果和描述。

    Args:
        wrapper: 包含用户信息的上下文包装器。
        sql: 要执行的SQL查询语句。
        description: 对SQL查询的简短描述。
        upload_to_feishu: 是否需要将查询结果上传到飞书（用户要求下载到Excel等操作也可以设置为True），默认为False。如果用户明确要求将结果上传到飞书，则设置为True。否则，设置为False。

    Returns:
        Tuple[SQLQueryResult, str]: 查询结果和对查询的描述。
    """
    # 获取用户信息用于并发控制
    user_info = wrapper.context
    user_id = user_info.email or user_info.user_name or "unknown_user"
    query_id = str(uuid.uuid4())

    logger.info(f"用户 {user_id} 开始执行SQL查询: {sql[:100]}...")

    # 使用用户级别的查询限制器，防止单个用户的慢查询影响其他用户
    result = await user_query_limiter.execute_with_limit(
        user_id=user_id,
        query_id=query_id,
        query_func=execute_business_query_async,
        sql_query=sql
    )

    if result is None:
        error_msg = f"查询被限制或超时，用户: {user_id}"
        logger.warning(error_msg)
        return None, f"{description} 执行失败: {error_msg}"

    if result.success:
        # Pass user_info from the wrapper context
        result = await upload_sql_result_to_feishu_if_needed(
            sql_result=result, sql_description=description, user_info=wrapper.context, upload_to_feishu=upload_to_feishu
        )
        return result, description
    else:
        # Return None for the result part of the tuple if the query failed
        return None, f"{description} 执行失败: {result.error}"


def get_sales_manager_team_members(bd_id = None, bd_name = None) -> List[Any]:
    """
    根据销售主管的ID或者名字，获取销售主管（M1或者M2）的团队成员，包括直接下属和间接下属的销售代表。

    参数:
        bd_id: 销售主管的ID。【注意，千万不可编造ID】
        bd_name: 销售主管的名字。【注意，千万不可编造名字】
        如果 bd_id 和 bd_name 都提供，优先使用 bd_id。

    返回:
        list: 销售主管的所有团队成员bd_id的列表。
    """
    if bd_id is None and bd_name is None:
        return []
    
    # 尝试将bd_id转换为整数
    query_str = f"bd_name = '{bd_name}'"
    try:
        if bd_id is not None:
            bd_id_int = int(bd_id)
            query_str = f"bd_id = {bd_id_int}"
    except (ValueError, TypeError):
        # 如果bd_id无法转换为整数，则使用bd_name
        logger.error(f"bd_id无法转换为整数: {bd_id}, 使用bd_name: {bd_name}")
            
    sql = f"""SELECT `bd_id` FROM `crm_bd_org` WHERE `parent_id` IN (
  SELECT id FROM crm_bd_org WHERE {query_str})
UNION ALL
SELECT `bd_id` FROM `crm_bd_org` WHERE `parent_id` IN (
  SELECT id FROM `crm_bd_org`
  WHERE `parent_id` IN (SELECT id FROM crm_bd_org WHERE {query_str}))"""
    result = execute_business_query(sql)
    if result.success:
        return result.data
    else:
        return []


def get_table_sample_data(table_name: str, sample_size: int = 2) -> SQLQueryResult:
    """
    获取指定表的样本数据。

    参数:
        table_name (str): 表名。
        sample_size (int): 样本大小，默认为2。

    使用示例:
        # 获取`orders`表的示例数据
        order_sample_data = get_table_sample_data('orders')

        # 获取最新的`merchants`表的示例数据
        order_sample_data = get_table_sample_data('merchants', 5)

    返回:
        dict: {"columns": columns, "data": list, "error": None}. list包含示例数据, columns为列名列表
    """
    return get_table_sample_data_real(table_name, sample_size)


def get_large_areas() -> List[Dict[str, Any]]:
    """
    获取公司当前定义的所有运营大区及其编号和名称。
    该工具主要用于：
    1. 当用户在提问中提及运营大区时，首先调用此工具获取所有有效的大区列表。
    2. 将用户提到的大区名称与列表中的实际大区名称进行匹配和验证。
    3. 如果用户提到的大区名称与列表中的实际大区名称不完全一致，AI应首先尝试进行智能匹配和纠正（例如，将 "杭粥大区" 自动识别为 "杭州大区"）。如果能够高置信度地匹配到一个有效大区，则可以直接使用纠正后的大区。只有当用户输入的大区名称偏差过大，完全无法匹配到任何已知大区时（例如 "美国大区"），才需要告知用户其输入的大区不存在，并可提示有效的大区选项。
    4. 对于用户可能提及的更广泛的地理区域（例如 "长三角地区"），AI可以尝试根据此工具返回的实际大区列表，智能地判断该广泛区域可能对应我司的哪一个或哪几个具体运营大区，并向用户确认。

    返回:
        list: 包含大区信息的字典列表，例如 [{'large_area_no': 'NO1', 'large_area_name': '杭州大区'}, ...]。
              如果查询失败或没有数据，则返回空列表。
    """
    sql = """
        SELECT
        large_area_no,
        large_area_name
        FROM
        large_area
        WHERE
        status = 1
        and `large_area_name` not LIKE '%测试%'
        and `large_area_name` not LIKE 'POP%';
    """
    result = execute_business_query(sql)
    if result.success:
        return result.data
    else:
        logger.error(f"Failed to get large areas: {result.error}")
        return []

tool_manager.register_as_function_tool(fetch_mysql_sql_result)
tool_manager.register_as_function_tool(get_sales_manager_team_members)
tool_manager.register_as_function_tool(get_table_sample_data)
tool_manager.register_as_function_tool(get_large_areas)

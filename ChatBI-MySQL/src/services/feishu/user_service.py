"""
飞书用户服务模块
负责处理用户信息获取、验证和管理
"""
import json
import lark_oapi as lark
from lark_oapi.api.contact.v3 import *
from lark_oapi.api.contact.v3.model import *

from src.utils.logger import logger
from src.services.auth.user_login_with_feishu import (
    upsert_user_info,
    get_user_admin_id
)
from src.services.feishu.message_apis import lark_client
from src.models.user_info_class import UserInfo
from utils.user_utils import get_api_token
from .token_service import TokenService


class UserService:
    """飞书用户服务类"""
    
    @staticmethod
    def get_valid_user_email(user_info: dict = {}) -> str:
        """获取有效的用户邮箱
        
        Args:
            user_info: 用户信息字典
            
        Returns:
            str: 有效的邮箱地址
        """
        email = user_info.get("email")
        if email and "@" in email:
            return email
        else:
            return user_info.get("enterprise_email", "unknown")
    
    @staticmethod
    def get_user_info(user_id: str) -> str | None:
        """使用tenant token获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            str | None: 用户信息的JSON字符串，失败时返回None
        """
        request: GetUserRequest = (
            GetUserRequest.builder()
            .user_id_type("open_id")
            .user_id(user_id)
            .department_id_type("open_department_id")
            .build()
        )

        response: GetUserResponse = lark_client.contact.v3.user.get(request)

        if not response.success():
            lark.logger.error(
                f"获取用户失败, user_id:{user_id}, reponse: {response.raw.content}"
            )
            return None

        return lark.JSON.marshal(response.data.user)
    
    @staticmethod
    def process_user_info(user_info_dict: dict) -> dict:
        """处理和完善用户信息
        
        Args:
            user_info_dict: 原始用户信息字典
            
        Returns:
            dict: 处理后的用户信息字典
        """
        # 确保admin_id存在
        if "admin_id" not in user_info_dict:
            name = user_info_dict.get("name")
            email = UserService.get_valid_user_email(user_info_dict)
            user_info_dict["admin_id"] = get_user_admin_id(email=email, user_name=name)
        
        # 添加API Token
        union_id = user_info_dict.get("union_id")
        if union_id:
            api_token = get_api_token(union_id=union_id)
            logger.info(f"获取到了 api_token:{api_token}")
            user_info_dict["summerfarm_api_token"] = api_token
        
        return user_info_dict
    
    @staticmethod
    def upsert_user_info_to_db(user_info_dict: dict, open_id: str):
        """将用户信息保存到数据库
        
        Args:
            user_info_dict: 用户信息字典
            open_id: 用户开放ID
        """
        upsert_user_info(
            name=user_info_dict.get("name", ""),
            email=user_info_dict.get("email", ""),
            user_id=user_info_dict.get("user_id", ""),
            job_title=user_info_dict.get("job_title", ""),
            open_id=open_id,
        )
    
    @staticmethod
    def create_user_info_object(user_info_dict: dict) -> UserInfo:
        """创建UserInfo对象
        
        Args:
            user_info_dict: 用户信息字典
            
        Returns:
            UserInfo: 用户信息对象
        """
        return UserInfo(
            user_name=user_info_dict.get("name"),
            email=UserService.get_valid_user_email(user_info_dict),
            access_token=TokenService.get_tenant_access_token(),
            union_id=user_info_dict.get("union_id"),
            summerfarm_api_token=user_info_dict.get("summerfarm_api_token")
        )

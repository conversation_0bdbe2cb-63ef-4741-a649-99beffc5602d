import json
from src.utils.logger import logger
from agents import ItemHelpers

THINKING_PROCESS_SPLITTER = "___\n"

def extract_content_from_stream_event(item):
    """从流事件中提取内容和日志信息

    注意：只有 raw_response_event 的内容会被添加到 full_response 中，
    其他事件类型只用于日志记录，避免内容重复。
    """
    content_chunk = None
    log_message = None

    try:
        logger.debug(f"开始处理流事件: {item.type}")
        if (
            item.type == "raw_response_event"
            and hasattr(item.data, "delta")
            and item.data.delta
        ):
            # 只有 raw_response_event 才返回内容用于添加到 full_response
            content_chunk, log_message = process_raw_response_event(item)
        elif item.type == "agent_thinking_event":
            log_message = f"Agent思考: {item.data.thinking}\n"
        elif item.type == "run_item_stream_event":
            # run_item_stream_event 只用于日志，不返回内容避免重复
            _, log_message = process_run_item_stream_event(item)
        elif item.type == "agent_updated_stream_event":
            # {item} 的内容很长，只取前200个字符用于日志
            item_str = str(item)
            short_item_str = item_str[:200] + ("..." if len(item_str) > 200 else "")
            logger.info(f"无须处理的事件类型: {item.type}, {short_item_str}")
            pass
        else:
            # 这里日志量很大，算了。
            # log_message = f"未处理的事件类型: {item.type}, {item}"
            pass
    except Exception as e:
        log_message = f"处理流事件时出错: {str(e)}"
        logger.error(log_message, exc_info=True)

    return content_chunk, log_message


def process_raw_response_event(item):
    """处理原始响应事件"""
    content_chunk = item.data.delta
    readable_msg = ""

    try:
        parsed = json.loads(content_chunk)
        if isinstance(parsed, dict):
            description = parsed.get("description")
            sql = parsed.get("sql")
            if description or sql:
                readable_msg = f"\n- 说明: {description}\n- SQL: {sql}\n{THINKING_PROCESS_SPLITTER}"
            else:
                # 因为要用工具使用与否来判断AI是否已经完成了思考过程，所以这里需要返回readable_msg
                readable_msg = "".join([f"- {key}: {value}\n" for key, value in parsed.items()])
                readable_msg = f"{readable_msg}\n{THINKING_PROCESS_SPLITTER}"
        else:
            readable_msg = f"{parsed}"
    except (json.JSONDecodeError, TypeError):
        # 可能就是正常的消息，比如一些AI的回复过程
        readable_msg = content_chunk

    return readable_msg, content_chunk


def process_run_item_stream_event(item):
    """处理运行项目流事件"""
    content_chunk = None
    log_message = None
    run_item = item.item

    if run_item.type == "tool_call_item":
        log_message = f"工具调用: {run_item.raw_item.name}"
    elif run_item.type == "tool_call_output_item":
        output = str(run_item.output)
        is_ddl_read_operator = "CREATE TABLE" in output or "TABLE_SCHEMA" in output
        log_message = (
            f"工具输出: {'AI读取了DDL文件' if is_ddl_read_operator else output}"
        )
    elif run_item.type == "handoff_output_item":
        # HandoffOutputItem doesn't have an output attribute, but we can access
        # the source and target agents
        source_agent = getattr(run_item, 'source_agent', None)
        target_agent = getattr(run_item, 'target_agent', None)

        if source_agent and target_agent:
            log_message = f"Handoff from {source_agent.name} to {target_agent.name}"
            logger.info(f"🔄 检测到Handoff: {source_agent.name} -> {target_agent.name}")
        else:
            log_message = "Handoff completed"
            logger.info("🔄 检测到Handoff完成（无详细信息）")
    elif run_item.type == "message_output_item":
        try:
            extracted_text = ItemHelpers.text_message_output(run_item)
            if extracted_text:
                content_chunk = extracted_text  # 将提取的文本作为内容块返回
                log_message = f"消息输出块: '{extracted_text}'"
            else:
                log_message = "收到空消息输出项。"
        except Exception as e:
            log_message = f"提取消息输出时出错: {str(e)}"
            logger.error(log_message, exc_info=True)
    else:
        log_message = f"未处理的run_item_stream_event项目类型: {run_item.type}"

    return content_chunk, f"{log_message}\n"

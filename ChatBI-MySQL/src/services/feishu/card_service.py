"""
飞书卡片服务模块
负责管理飞书卡片的创建、更新和完成
"""
import asyncio
from src.utils.logger import logger
from src.services.feishu.message_apis import (
    reply_simple_text_message,
    initial_card_message,
    send_updates_to_card,
    send_finished_message_to_card,
    FINAL_ELEMENT_ID,
    THINKING_ELEMENT_ID,
)
from src.services.feishu.agent_message_formatter import THINKING_PROCESS_SPLITTER
from .config import FeishuConfig


class CardService:
    """卡片服务类"""
    
    @staticmethod
    async def create_initial_card_early(
        message_id: str, user_query: str, conversation_id: str, user_id: str
    ) -> tuple[str, str]:
        """尽早创建初始卡片，让用户快速收到响应
        
        Args:
            message_id: 消息ID
            user_query: 用户查询
            conversation_id: 对话ID
            user_id: 用户ID
            
        Returns:
            tuple: (card_id, element_id) 如果创建失败则返回 (None, None)
        """
        card_id, element_id = initial_card_message(
            message_id,
            "🤖 正在思考中，请稍候...",
            user_query=user_query,
            chat_id=conversation_id,
            user_id=user_id,
        )
        if not card_id:
            logger.error("无法初始化卡片消息，处理中止。")
            reply_simple_text_message(
                message_id, "抱歉，创建回复卡片时出错，请稍后再试。"
            )
            return None, None
        
        return card_id, element_id
    
    @staticmethod
    def split_thinking_and_reply_content(full_response: str) -> tuple[str, str | None]:
        """分离思考过程和最终回复内容
        
        Args:
            full_response: 完整的响应内容
            
        Returns:
            tuple: (thinking_markdown, reply_markdown)
                   如果没有分隔符，reply_markdown为None
        """
        
        if THINKING_PROCESS_SPLITTER in full_response:
            parts = full_response.split(THINKING_PROCESS_SPLITTER)
            reply_markdown = parts[-1]
            thinking_markdown = THINKING_PROCESS_SPLITTER.join(parts[:-1])
            return thinking_markdown, reply_markdown
        else:
            return full_response, None
    
    @staticmethod
    async def send_thinking_and_reply_updates(
        card_id: str,
        full_response: str,
        current_sequence: int,
        force_send_thinking: bool = False
    ) -> int:
        """发送思考过程和最终回复的更新到卡片
        
        Args:
            card_id: 卡片ID
            full_response: 完整响应内容
            current_sequence: 当前序列号
            force_send_thinking: 是否强制发送思考过程（用于最终更新）
            
        Returns:
            int: 更新后的序列号
        """
        thinking_markdown, reply_markdown = CardService.split_thinking_and_reply_content(full_response)
        
        # 决定是否发送思考过程
        should_send_thinking = (
            force_send_thinking or
            not reply_markdown or
            len(reply_markdown) < FeishuConfig.get_message_steps()
        )
        
        if should_send_thinking:
            current_sequence = await asyncio.to_thread(
                send_updates_to_card,
                card_id=card_id,
                markdown_content=thinking_markdown,
                element_id=THINKING_ELEMENT_ID,
                sequence=current_sequence,
            )
        
        # 如果没有回复内容，发送思考过程的后20个字符作为回复内容
        _final_reply = f"🚀思考中...第{current_sequence}步: {thinking_markdown[-20:]}"
        if reply_markdown:
            _final_reply = reply_markdown

        current_sequence = await asyncio.to_thread(
            send_updates_to_card,
            card_id=card_id,
            markdown_content=_final_reply,
            element_id=FINAL_ELEMENT_ID,
            sequence=current_sequence,
        )

        return current_sequence
    
    @staticmethod
    async def handle_retry_logic(
        card_id: str,
        current_sequence: int,
        retry_count: int,
        retry_interval: int
    ) -> int:
        """处理重试逻辑，发送重试消息并等待
        
        Args:
            card_id: 卡片ID
            current_sequence: 当前序列号
            retry_count: 重试次数
            retry_interval: 重试间隔（秒）
            
        Returns:
            int: 更新后的序列号
        """
        max_retries, _ = FeishuConfig.get_retry_config()
        logger.warning(f"未检测到Handoff日志，准备重试 (重试 {retry_count}/{max_retries})")
        
        # 更新卡片，告知用户正在重试
        current_sequence += 1
        retry_message = "🤖发生了一点小故障，重试中..."
        current_sequence = await asyncio.to_thread(
            send_updates_to_card,
            card_id=card_id,
            markdown_content=retry_message,
            element_id=THINKING_ELEMENT_ID,
            sequence=current_sequence,
        )
        
        # 异步等待重试间隔
        await asyncio.sleep(retry_interval)
        
        return current_sequence
    
    @staticmethod
    async def create_timeout_card(
        message_id: str, user_query: str, conversation_id: str, user_id: str
    ) -> tuple[str, str]:
        """创建超时后的新卡片
        
        Args:
            message_id: 消息ID
            user_query: 用户查询
            conversation_id: 对话ID
            user_id: 用户ID
            
        Returns:
            tuple: (card_id, element_id) 如果创建失败则返回 (None, None)
        """
        new_card_id, new_element_id = initial_card_message(
            message_id,
            "🕒 响应超时，已重新创建卡片",
            user_query=user_query,
            chat_id=conversation_id,
            user_id=user_id,
        )
        if not new_card_id:
            host_name = FeishuConfig.get_host_name()
            await asyncio.to_thread(
                reply_simple_text_message,
                message_id,
                f"您的任务：{user_query}\n🤖 响应超时，尝试重新创建卡片，但创建新卡片失败。\n请您去网页版查看结果：{host_name}?chat={conversation_id}",
            )
            return None, None
        
        return new_card_id, new_element_id
    
    @staticmethod
    async def finish_card(card_id: str, sequence: int, conversation_id: str):
        """完成卡片处理
        
        Args:
            card_id: 卡片ID
            sequence: 序列号
            conversation_id: 对话ID
        """
        if sequence > 0:
            logger.info(f"即将完成卡片 {card_id}，最终设置序列号 {sequence}")
            await asyncio.to_thread(
                send_finished_message_to_card,
                card_id=card_id,
                sequence=sequence,
                chat_id=conversation_id,
            )
        else:
            logger.warning(f"由于流式响应超时，跳过完成卡片步骤")

"""
飞书Token服务模块
负责管理飞书API的Token获取和管理
"""

import requests
from src.utils.logger import logger
from src.services.auth.user_login_with_feishu import APP_ID, APP_SECRET
from src.utils.in_memory_cache import in_memory_cache


class TokenService:
    """飞书Token服务类"""

    @staticmethod
    @in_memory_cache(expire_seconds=3600)
    def get_feishu_token() -> dict:
        """获取飞书访问Token

        Returns:
            dict: 包含Token信息的字典
        """
        url = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
        try:
            token = requests.post(
                url=url,
                json={"app_id": APP_ID, "app_secret": APP_SECRET},
            ).json()
            logger.info(f"feishu tenant_token:{token}")
            return token
        except Exception as e:
            logger.error(f"获取飞书Token失败: {e}", exc_info=True)
            return {}

    @staticmethod
    def get_tenant_access_token() -> str:
        """获取租户访问Token

        Returns:
            str: 租户访问Token，如果获取失败返回空字符串
        """
        token_info = TokenService.get_feishu_token()
        return token_info.get("tenant_access_token", "")
